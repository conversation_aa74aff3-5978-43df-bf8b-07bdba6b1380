#!/usr/bin/env python3
"""
Signal Tracking System for Trading Bot
Tracks signals, evaluates results, and maintains statistics
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from utils import print_colored, fetch_live_candles, fetch_candle_by_timestamp
import pandas as pd


class SignalTracker:
    """Tracks trading signals and evaluates their results"""
    
    def __init__(self, data_file="signal_data.json"):
        self.data_file = data_file
        self.signals = []  # List of active signals waiting for evaluation
        self.completed_signals = []  # List of completed signals with results
        self.stats = {
            'total_signals': 0,
            'total_wins': 0,
            'total_losses': 0,
            'total_refunds': 0
        }
        self.load_data()
    
    def load_data(self):
        """Load existing signal data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    self.signals = data.get('signals', [])
                    self.completed_signals = data.get('completed_signals', [])
                    self.stats = data.get('stats', {
                        'total_signals': 0,
                        'total_wins': 0,
                        'total_losses': 0,
                        'total_refunds': 0
                    })
                    print_colored(f"📊 Loaded {len(self.signals)} pending signals and {len(self.completed_signals)} completed signals", "INFO")
        except Exception as e:
            print_colored(f"⚠️ Could not load signal data: {str(e)}", "WARNING")
            self.signals = []
            self.completed_signals = []
            self.stats = {
                'total_signals': 0,
                'total_wins': 0,
                'total_losses': 0,
                'total_refunds': 0
            }
    
    def save_data(self):
        """Save signal data to file"""
        try:
            data = {
                'signals': self.signals,
                'completed_signals': self.completed_signals,
                'stats': self.stats
            }
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            print_colored(f"❌ Error saving signal data: {str(e)}", "ERROR")
    
    def add_signal(self, pair: str, direction: str, signal_timestamp: datetime, open_price: float, 
                   confidence: float = None, strategy: str = None) -> str:
        """
        Add a new signal for tracking
        
        Args:
            pair: Currency pair (e.g., "EURUSD")
            direction: "UP" or "DOWN"
            signal_timestamp: When the signal was generated
            open_price: The open price of the next 1-minute candle
            confidence: Signal confidence (optional)
            strategy: Strategy name (optional)
            
        Returns:
            Signal ID for reference
        """
        signal_id = f"{pair}_{signal_timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        # Calculate the evaluation timestamp (1 minute after signal)
        evaluation_timestamp = signal_timestamp + timedelta(minutes=1)
        
        signal = {
            'id': signal_id,
            'pair': pair,
            'direction': direction,
            'signal_timestamp': signal_timestamp.isoformat(),
            'evaluation_timestamp': evaluation_timestamp.isoformat(),
            'open_price': open_price,
            'confidence': confidence,
            'strategy': strategy,
            'status': 'PENDING'
        }
        
        self.signals.append(signal)
        self.stats['total_signals'] += 1
        self.save_data()
        
        print_colored(f"📝 Signal added: {pair} {direction} at {signal_timestamp.strftime('%H:%M:%S')}", "INFO")
        return signal_id
    
    def fetch_candle_open_close(self, pair: str, timestamp: datetime) -> Optional[Tuple[float, float]]:
        """
        Fetch the open and close price of a specific 1-minute candle

        Args:
            pair: Currency pair
            timestamp: The start time of the candle to fetch

        Returns:
            Tuple of (open_price, close_price) or None if not found
        """
        try:
            # Convert pair format for Oanda API (e.g., EURUSD -> EUR_USD)
            oanda_pair = f"{pair[:3]}_{pair[3:]}" if len(pair) == 6 else pair

            # Try to fetch specific candle by timestamp first
            result = fetch_candle_by_timestamp(oanda_pair, timestamp)
            if result:
                return result

            # Fallback: Fetch recent candles and find the closest match
            df = fetch_live_candles(oanda_pair, count=10)

            if df is None or len(df) == 0:
                return None

            # Convert timestamp to string format matching the data
            target_time_str = timestamp.strftime('%Y-%m-%d %H:%M:00')

            # Find the candle that matches our timestamp
            matching_candle = df[df['time'] == target_time_str]

            if len(matching_candle) > 0:
                candle = matching_candle.iloc[0]
                return (float(candle['open']), float(candle['close']))
            else:
                # If exact match not found, try to find the closest candle
                df['datetime'] = pd.to_datetime(df['time'])
                target_datetime = pd.to_datetime(target_time_str)

                # Find the closest candle within 2 minutes
                time_diff = abs(df['datetime'] - target_datetime)
                closest_idx = time_diff.idxmin()

                if time_diff.loc[closest_idx].total_seconds() <= 120:  # Within 2 minutes
                    candle = df.loc[closest_idx]
                    return (float(candle['open']), float(candle['close']))

            return None

        except Exception as e:
            print_colored(f"❌ Error fetching candle data for {pair} at {timestamp}: {str(e)}", "ERROR")
            return None

    def evaluate_pending_signals(self) -> List[Dict]:
        """
        Evaluate all pending signals that are ready for evaluation

        Returns:
            List of evaluation results
        """
        current_time = datetime.now()
        evaluated_signals = []
        signals_to_remove = []

        for i, signal in enumerate(self.signals):
            evaluation_time = datetime.fromisoformat(signal['evaluation_timestamp'])

            # Check if it's time to evaluate this signal (allow 30 second buffer)
            if current_time >= evaluation_time - timedelta(seconds=30):
                result = self.evaluate_signal(signal)
                if result:
                    evaluated_signals.append(result)
                    signals_to_remove.append(i)

        # Remove evaluated signals from pending list
        for i in reversed(signals_to_remove):
            completed_signal = self.signals.pop(i)
            self.completed_signals.append(completed_signal)

        if evaluated_signals:
            self.save_data()

        return evaluated_signals

    def evaluate_signal(self, signal: Dict) -> Optional[Dict]:
        """
        Evaluate a single signal

        Args:
            signal: Signal dictionary

        Returns:
            Evaluation result dictionary or None if evaluation failed
        """
        try:
            pair = signal['pair']
            direction = signal['direction']
            evaluation_timestamp = datetime.fromisoformat(signal['evaluation_timestamp'])

            # Fetch the candle data for evaluation
            candle_data = self.fetch_candle_open_close(pair, evaluation_timestamp)

            if candle_data is None:
                print_colored(f"⚠️ Could not fetch candle data for {pair} at {evaluation_timestamp.strftime('%H:%M:%S')}", "WARNING")
                return None

            open_price, close_price = candle_data

            # Determine the result
            if close_price == open_price:
                result = "REFUND"
                self.stats['total_refunds'] += 1
            elif direction == "UP" and close_price > open_price:
                result = "WIN"
                self.stats['total_wins'] += 1
            elif direction == "DOWN" and close_price < open_price:
                result = "WIN"
                self.stats['total_wins'] += 1
            else:
                result = "LOSS"
                self.stats['total_losses'] += 1

            # Update signal with result
            signal['result'] = result
            signal['evaluation_open'] = open_price
            signal['evaluation_close'] = close_price
            signal['status'] = 'COMPLETED'
            signal['evaluated_at'] = datetime.now().isoformat()

            evaluation_result = {
                'pair': pair,
                'direction': direction,
                'result': result,
                'signal_time': signal['signal_timestamp'],
                'evaluation_time': evaluation_timestamp.isoformat(),
                'open_price': open_price,
                'close_price': close_price,
                'confidence': signal.get('confidence'),
                'strategy': signal.get('strategy')
            }

            return evaluation_result

        except Exception as e:
            print_colored(f"❌ Error evaluating signal: {str(e)}", "ERROR")
            return None

    def get_latest_result(self) -> Optional[Dict]:
        """Get the most recent signal result"""
        if not self.completed_signals:
            return None

        # Find the most recently completed signal
        latest_signal = max(self.completed_signals,
                          key=lambda x: x.get('evaluated_at', ''))

        if latest_signal.get('result'):
            return {
                'pair': latest_signal['pair'],
                'direction': latest_signal['direction'],
                'result': latest_signal['result'],
                'signal_time': latest_signal['signal_timestamp'],
                'confidence': latest_signal.get('confidence'),
                'strategy': latest_signal.get('strategy')
            }

        return None

    def get_stats(self) -> Dict:
        """Get current statistics"""
        return self.stats.copy()

    def format_result_display(self, result: Dict) -> str:
        """
        Format result for display

        Args:
            result: Result dictionary from get_latest_result()

        Returns:
            Formatted result string
        """
        if not result:
            return ""

        # Determine result icon and color
        if result['result'] == 'WIN':
            icon = "✅"
            result_text = "WIN"
        elif result['result'] == 'LOSS':
            icon = "❌"
            result_text = "LOSS"
        else:  # REFUND
            icon = "🔄"
            result_text = "REFUND"

        # Parse timestamp for display
        try:
            signal_dt = datetime.fromisoformat(result['signal_time'])
            time_str = signal_dt.strftime('%H:%M:%S')
        except:
            time_str = "Unknown"

        return f"{icon} Result: {result_text} | {result['pair']} | {result['direction']} | {time_str}"

    def format_stats_display(self) -> str:
        """Format statistics for display"""
        stats = self.get_stats()
        return f"📊 Stats → Total: {stats['total_signals']} | Wins: {stats['total_wins']} | Losses: {stats['total_losses']} | Refunds: {stats['total_refunds']}"
