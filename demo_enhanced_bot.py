#!/usr/bin/env python3
"""
Demo script to show enhanced bot features
"""

from datetime import datetime, timedelta
from signal_tracker import SignalTracker
from utils import print_colored, print_header
import time

def demo_signal_flow():
    """Demonstrate the signal tracking flow"""
    print_header("🎯 ENHANCED TRADING BOT DEMO")
    
    # Create a demo tracker
    tracker = SignalTracker("demo_signals.json")
    
    print_colored("This demo shows how the enhanced trading bot works:", "INFO", bold=True)
    print()
    
    # Simulate first signal
    print_colored("📊 MARKET SCAN - 12:31:58", "HEADER", bold=True)
    print_colored("Generating first signal...", "INFO")
    
    signal_time = datetime.now() - timedelta(minutes=2)  # Simulate past signal
    tracker.add_signal(
        pair="EURUSD",
        direction="DOWN",
        signal_timestamp=signal_time,
        open_price=1.0850,
        confidence=0.75,
        strategy="S2"
    )
    
    print_colored("📉 SELL signal generated for EURUSD", "SELL")
    print()
    
    # Simulate second scan with result
    print_colored("📊 MARKET SCAN - 12:32:58", "HEADER", bold=True)
    print_colored("Evaluating previous signal and generating new signal...", "INFO")
    
    # Manually create a result for demo (since we don't have real market data)
    demo_result = {
        'pair': 'EURUSD',
        'direction': 'DOWN',
        'result': 'WIN',
        'signal_time': signal_time.isoformat(),
        'confidence': 0.75,
        'strategy': 'S2'
    }
    
    # Update stats manually for demo
    tracker.stats['total_wins'] += 1
    
    print_colored(tracker.format_result_display(demo_result), "SUCCESS")
    print_colored(tracker.format_stats_display(), "INFO")
    print()
    
    # Add another signal
    signal_time2 = datetime.now() - timedelta(minutes=1)
    tracker.add_signal(
        pair="GBPUSD",
        direction="UP",
        signal_timestamp=signal_time2,
        open_price=1.2750,
        confidence=0.68,
        strategy="S1"
    )
    
    print_colored("📈 BUY signal generated for GBPUSD", "BUY")
    print()
    
    # Simulate third scan
    print_colored("📊 MARKET SCAN - 12:33:58", "HEADER", bold=True)
    print_colored("Evaluating previous signal...", "INFO")
    
    # Create another demo result
    demo_result2 = {
        'pair': 'GBPUSD',
        'direction': 'UP',
        'result': 'LOSS',
        'signal_time': signal_time2.isoformat(),
        'confidence': 0.68,
        'strategy': 'S1'
    }
    
    # Update stats manually for demo
    tracker.stats['total_losses'] += 1
    
    print_colored(tracker.format_result_display(demo_result2), "SUCCESS")
    print_colored(tracker.format_stats_display(), "INFO")
    print()
    
    print_colored("✅ Demo completed!", "SUCCESS", bold=True)
    print_colored("This is how the enhanced bot tracks and evaluates signals in real-time.", "INFO")

def show_features():
    """Show the key features"""
    print_header("🚀 KEY FEATURES")
    
    features = [
        "📝 Automatic signal storage with timestamps",
        "🔍 Real-time result evaluation after 1 minute",
        "📊 Persistent win/loss/refund statistics",
        "🎯 Previous signal results displayed before each scan",
        "💾 Data saved across bot restarts",
        "🔄 Works with both Rule-Based and ML-Enhanced bots",
        "📈 Real-time performance tracking",
        "🎨 Enhanced visual display with result icons"
    ]
    
    for feature in features:
        print_colored(feature, "SUCCESS")
    
    print()
    print_colored("Ready to use with your live trading!", "INFO", bold=True)

def main():
    """Main demo function"""
    try:
        demo_signal_flow()
        print()
        show_features()
        
        print()
        print_header("🎉 ENHANCED BOT READY")
        print_colored("Run 'python trading_bot_launcher.py' to start trading with enhanced features!", "SUCCESS", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Demo failed: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
