#!/usr/bin/env python3
"""
ML-Integrated Live Trading Bot with Oanda API Integration
Combines Machine Learning predictions with rule-based strategies
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from ml_strategy_engine import MLStrategyEngine
from signal_tracker import SignalTracker
from utils import (
    fetch_live_candles, get_current_time_info, print_colored,
    print_header, print_table_row, format_signal_output, validate_pair,
    print_ml_signal_table_header, print_ml_signal_row, fetch_candle_by_timestamp
)
from config import CURRENCY_PAIRS, TRADING_CONFIG, DISPLAY_CONFIG

class MLTradingBot:
    def __init__(self, selected_pairs=None):
        """Initialize the ML-integrated trading bot"""
        self.strategy_engine = MLStrategyEngine()
        self.signal_tracker = SignalTracker("ml_bot_signals.json")
        self.running = False
        self.pairs = selected_pairs if selected_pairs else CURRENCY_PAIRS.copy()
        
    def start(self):
        """Start the ML-integrated trading bot"""
        print_header("🧠 ML-INTEGRATED TRADING BOT STARTED")
        print_colored(f"🤖 AI-Enhanced: Machine Learning + Rule-based strategies", "SUCCESS", bold=True)
        print_colored(f"📊 Monitoring {len(self.pairs)} currency pairs", "INFO")
        print_colored(f"⏰ Fetching data every {TRADING_CONFIG['FETCH_INTERVAL']} seconds", "INFO")
        print_colored(f"🎯 ML Confidence threshold: {TRADING_CONFIG['MIN_CONFIDENCE']*100}%", "INFO")
        print_colored(f"🔧 Enhanced with ensemble AI models", "SUCCESS")
        print()
        
        self.running = True
        
        # Start the main trading loop
        self.trading_loop()
    
    def stop(self):
        """Stop the trading bot"""
        self.running = False
        print_colored("\n🛑 ML Trading bot stopped", "WARNING")
    
    def trading_loop(self):
        """Main trading loop"""
        while self.running:
            try:
                # Get current time info
                time_info = get_current_time_info()
                current_minute = time_info['current_time'].minute

                # Wait until 2 seconds before next minute
                if time_info['seconds_to_next_minute'] > 2:
                    sleep_time = time_info['seconds_to_next_minute'] - 2
                    print_colored(f"⏳ AI analysis in {sleep_time} seconds...", "INFO")
                    time.sleep(sleep_time)

                # Perform ML-enhanced market scan
                self.scan_markets_with_ml()

                # Wait until the next minute starts (ensuring we don't skip minutes)
                while True:
                    time_info = get_current_time_info()
                    new_minute = time_info['current_time'].minute

                    # If minute has changed, break and start next scan
                    if new_minute != current_minute:
                        break

                    # Sleep for 1 second and check again
                    time.sleep(1)

            except KeyboardInterrupt:
                print_colored("\n⚠️  Interrupted by user", "WARNING")
                break
            except Exception as e:
                print_colored(f"❌ Error in ML trading loop: {str(e)}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def scan_markets_with_ml(self):
        """Scan all currency pairs for trading signals using ML"""
        current_time = datetime.now()

        # First, evaluate any pending signals from previous runs
        evaluated_results = self.signal_tracker.evaluate_pending_signals()

        # Display header with previous signal result if available
        print_header(f"🧠 ML-ENHANCED MARKET SCAN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Show previous signal result if available
        latest_result = self.signal_tracker.get_latest_result()
        if latest_result:
            print_colored(self.signal_tracker.format_result_display(latest_result), "SUCCESS")
            print_colored(self.signal_tracker.format_stats_display(), "INFO")
            print()

        # Print ML signal table header
        print_ml_signal_table_header()

        signals_found = 0

        for pair in self.pairs:
            try:
                # Fetch live data
                df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])

                if df is not None and len(df) > 50:
                    # Evaluate with ML-enhanced strategies
                    signal_data = self.strategy_engine.evaluate_ml_enhanced_strategies(df)

                    # Format output
                    formatted_output = format_signal_output(pair, signal_data)

                    # Display result in horizontal format
                    if signal_data['signal'] != 'HOLD':
                        signals_found += 1

                        # Get individual signals for display
                        ml_signal = signal_data.get('ml_signal', 'HOLD')
                        rule_signal = signal_data.get('rule_signal', 'HOLD')

                        print_ml_signal_row(
                            date=formatted_output['date'],
                            time=formatted_output['time'],
                            pair=pair,
                            price=formatted_output['price'],
                            final_signal=signal_data['signal'],
                            final_confidence=formatted_output['confidence'],
                            ml_signal=ml_signal,
                            ml_confidence=f"{signal_data.get('ml_confidence', 0)*100:.1f}%",
                            rule_signal=rule_signal,
                            rule_confidence=f"{signal_data.get('rule_confidence', 0)*100:.1f}%",
                            strategy=signal_data.get('strategy', 'Unknown'),
                            is_no_signal=False
                        )
                    else:
                        # No signal found
                        ml_signal = signal_data.get('ml_signal', 'HOLD')
                        rule_signal = signal_data.get('rule_signal', 'HOLD')

                        print_ml_signal_row(
                            date=formatted_output['date'],
                            time=formatted_output['time'],
                            pair=pair,
                            price=formatted_output['price'],
                            is_no_signal=True
                        )
                else:
                    # Data fetch failed - show as no signal
                    time_info = get_current_time_info()
                    print_ml_signal_row(
                        date=time_info['current_time'].strftime('%Y-%m-%d'),
                        time=time_info['current_time'].strftime('%H:%M:%S'),
                        pair=pair,
                        price="ERROR",
                        is_no_signal=True
                    )

            except Exception as e:
                print_colored(f"❌ Error scanning {pair}: {str(e)}", "ERROR")

        # Summary
        print_colored("=" * 110, "HEADER")
        if signals_found > 0:
            print_colored(f"🤖 Found {signals_found} ML-enhanced trading signals", "SUCCESS", bold=True)
        else:
            print_colored("🤖 No ML-enhanced trading signals found", "INFO")

        # Add signals to tracker in separate block
        if signals_found > 0:
            print()
            print_colored("📝 SIGNAL TRACKING", "HEADER", bold=True)
            print_colored("=" * 60, "HEADER")

            # Re-scan to add signals to tracker
            for pair in self.pairs:
                try:
                    df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'])
                    if df is not None and len(df) > 50:
                        signal_data = self.strategy_engine.evaluate_ml_enhanced_strategies(df)

                        if signal_data['signal'] != 'HOLD':
                            # Add signal to tracker
                            direction = "UP" if signal_data['signal'] == "BUY" else "DOWN"

                            # Use current minute as signal time (rounded down)
                            signal_time = current_time.replace(second=0, microsecond=0)

                            signal_id = self.signal_tracker.add_signal(
                                pair=pair,
                                direction=direction,
                                signal_timestamp=signal_time,
                                open_price=signal_data['price'],
                                confidence=signal_data['confidence'],
                                strategy=signal_data.get('strategy', 'ML-Enhanced')
                            )

                            print_colored(f"📝 Signal tracked: {pair} {direction} at {signal_time.strftime('%H:%M:%S')}", "SUCCESS")

                except Exception as e:
                    continue

        print()
    
    def log_ml_signal(self, formatted_output, signal_data):
        """Log detailed ML signal information"""
        print_colored(f"\n🤖 ML-ENHANCED SIGNAL DETECTED:", "SUCCESS", bold=True)
        print_colored(f"   📅 Date: {formatted_output['date']}", "INFO")
        print_colored(f"   🕐 Time: {formatted_output['time']}", "INFO")
        print_colored(f"   💱 Pair: {formatted_output['pair']}", "INFO")
        print_colored(f"   📈 Final Direction: {signal_data['signal']}", formatted_output['color'], bold=True)
        print_colored(f"   💰 Price: {formatted_output['price']}", "INFO")
        print_colored(f"   🎯 Final Confidence: {formatted_output['confidence']}", "SUCCESS")
        
        # Show ML vs Rule-based breakdown
        print_colored(f"   🧠 ML Prediction: {signal_data.get('ml_signal', 'HOLD')} ({signal_data.get('ml_confidence', 0)*100:.1f}%)", "SUCCESS")
        print_colored(f"   📊 Rule-based: {signal_data.get('rule_signal', 'HOLD')} ({signal_data.get('rule_confidence', 0)*100:.1f}%)", "INFO")
        print_colored(f"   🔧 Decision Method: {signal_data.get('decision_method', 'Unknown')}", "INFO")
        
        # Show all strategy signals
        print_colored(f"   📊 Strategy Breakdown:", "INFO")
        for strategy, data in signal_data['all_signals'].items():
            signal_text = "BUY" if data['signal'] == 1 else "SELL" if data['signal'] == -1 else "HOLD"
            conf_text = f"{data['confidence']*100:.1f}%" if data['confidence'] > 0 else "-"
            print_colored(f"      {strategy}: {signal_text} ({conf_text})", "INFO")
        print()

def main(selected_pairs=None):
    """Main function"""
    try:
        # Create and start the ML trading bot
        bot = MLTradingBot(selected_pairs)

        print_colored("🤖 ML-Integrated Trading Bot Initializing...", "SUCCESS", bold=True)
        print_colored("🧠 Loading AI models and rule-based strategies...", "INFO")
        print_colored("Press Ctrl+C to stop the bot", "WARNING")
        print()

        # Start the bot
        bot.start()

    except KeyboardInterrupt:
        print_colored("\n👋 Goodbye! ML Trading bot stopped.", "INFO")
    except Exception as e:
        print_colored(f"❌ Fatal error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
